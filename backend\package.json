{"name": "stes-backend", "version": "1.0.0", "description": "Backend for STES Swimming Pool E-commerce", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seedData.js", "seed:customers": "node scripts/seedCustomers.js"}, "keywords": ["ecommerce", "swimming-pool", "tunisia", "nodejs", "express"], "author": "STES Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7"}, "devDependencies": {"nodemon": "^3.0.2"}}